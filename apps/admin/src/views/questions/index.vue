<script setup lang="ts">
import LeftView from './components/left-view/index.vue'
import RightView from './components/right-view/index.vue'

defineOptions({
  name: 'Questions',
})
</script>

<template>
  <div class="relative h-full w-full flex-col-stretch">
    <!-- 背景 -->
    <div class="bg-container-view absolute inset-0 z-0 h-full" />
    <NCard :bordered="false" size="small" class="card-wrapper">
      <NGrid responsive="screen" :x-gap="20" :y-gap="20" item-responsive>
        <NGridItem span="24 l:8">
          <LeftView />
        </NGridItem>
        <NGridItem span="24 l:16">
          <RightView />
        </NGridItem>
      </NGrid>
    </NCard>
  </div>
</template>

<style lang="scss">
.bg-container-view{
  background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGa2cc791875d5eb4639bbba2971f330fc.png) 100% no-repeat;
  background-size: 100% 100%;
}
</style>
