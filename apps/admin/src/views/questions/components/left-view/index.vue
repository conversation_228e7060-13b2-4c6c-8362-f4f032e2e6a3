<script setup lang="ts">
import { getAgentModelInfo } from '@/service/api'

defineOptions({
  name: 'LeftView',
})
// 创建方式选项
const createMethods = [
  { key: 'title', label: '知识点出题 ', active: true },
  { key: 'text', label: '文本出题', active: false },
  { key: 'chapter', label: '附件出题', active: false },
  { key: 'document', label: '章节出题', active: false },
]
// 内容要求选项
const requirementOptions = [
  { key: '学情分析', label: '学情分析' },
  { key: '教学目标', label: '教学目标' },
  { key: '课程重点', label: '课程重点' },
  { key: '课程难点', label: '课程难点' },
  { key: '教学准备', label: '教学准备' },
]
// AI模型选择选项
const modelOptions = ref([] as QuestionsApi.AgentModelInfoResponse[])

const activeCreateMethod = ref('title')

// 表单数据
const formModel = ref({
  Mode: '', // 模型
  grade: null as string | null,
  title: '',
  requirements: ['学情分析'],
  otherRequirements: '',
})
// 处理模型选择
function handleModelSelect(key: string) {
  formModel.value.Mode = key
}
// 处理创建方式切换
function switchCreateMethod(method: string) {
  activeCreateMethod.value = method
}
function generateLesson() {

}
function initData() {
  getAgentModelInfo().then((res) => {
    if (res.data) {
      modelOptions.value = res.data
      formModel.value.Mode = res.data[0].Id
    }
  })
}
// 年级选择选项
const gradeOptions = ref([
  { label: '一年级', value: '1' },
  { label: '二年级', value: '2' },
  { label: '三年级', value: '3' },
  { label: '四年级', value: '4' },
  { label: '五年级', value: '5' },
  { label: '六年级', value: '6' },
])
onMounted(() => {
  initData()
})
</script>

<template>
  <div class="bg-#F5FBFE">
    <div class="flex-shrink-0">
      <!-- 标题栏 -->
      <div class="mb-16px flex items-center justify-between">
        <div class="flex items-center gap-8px">
          <div class="h-38px w-38px flex items-center justify-center rounded-8px from-blue-500 to-purple-500 bg-gradient-to-r">
            <SvgIcon icon="mdi:robot-outline" class="text-20px text-white" />
          </div>
          <span class="from-blue-500 to-purple-500 bg-gradient-to-r bg-clip-text text-22px text-transparent font-600">
            AI教案助手
          </span>
        </div>

        <!-- AI模型选择下拉框 -->
        <NDropdown
          trigger="click"
          key-field="Id"
          label-field="ModelName"
          :options="modelOptions"
          :show-arrow="true"
          @select="handleModelSelect"
        >
          <NButton quaternary class="flex items-center gap-6px rounded-8px bg-blue-100 px-12px py-8px hover:bg-blue-200">
            <SvgIcon icon="mdi:brain" class="mr-4px text-16px text-blue-600" />
            <span class="text-14px text-blue-700">{{ formModel.Mode ? modelOptions.find(m => m.Id === formModel.Mode)?.ModelName : '选择模型' }}</span>
            <SvgIcon icon="mdi:chevron-down" class="text-12px text-blue-600" />
          </NButton>
        </NDropdown>
      </div>

      <!-- 创建方式选项卡 -->
      <div class="mb-16px flex justify-between rounded-8px bg-blue-100 p-4px">
        <div
          v-for="method in createMethods"
          :key="method.key"
          class="cursor-pointer rounded-6px px-16px py-8px text-14px font-500 transition-all duration-200"
          :class="[
            activeCreateMethod === method.key
              ? 'bg-white text-blue-600 shadow-sm '
              : ' hover:text-blue-600',
          ]"
          @click="switchCreateMethod(method.key)"
        >
          {{ method.label }}
        </div>
      </div>
    </div>
    <!-- 内容区域 -->
    <div class="min-h-0 flex-shrink-1">
      <NScrollbar class="h-full">
        <!-- 表单区域 -->
        <NForm
          :model="formModel"
          label-placement="top"
          size="medium"
        >
          <!-- 年级选择 -->
          <NFormItem label="年级" path="grade" required>
            <template #label>
              <span class="text-left text-14px text-[#464646] font-500">年级</span>
            </template>
            <NSelect
              v-model:value="formModel.grade"
              :options="gradeOptions"
              placeholder="请选择年级"
              clearable
            />
          </NFormItem>
          <!-- 教案标题 -->
          <NFormItem label="教案标题" path="title" required>
            <template #label>
              <span class="text-left text-14px text-[#464646] font-500">教案标题</span>
            </template>
            <NInput
              v-model:value="formModel.title"
              placeholder="请输入教案标题"
              clearable
              maxlength="100"
              show-count
            />
          </NFormItem>
        </NForm>

        <!-- 内容要求 -->
        <NFormItem label="内容要求" path="requirements" required>
          <template #label>
            <span class="text-left text-14px text-[#464646] font-500">内容要求</span>
          </template>
          <div class="flex flex-wrap gap-8px">
            <button
              v-for="option in requirementOptions"
              :key="option.key"
              class="border rounded-6px px-12px py-6px text-14px font-500 transition-all duration-200"
              :class="[
                formModel.requirements.includes(option.key)
                  ? 'border-purple-300 bg-purple-100 text-purple-700 '
                  : 'border-gray-300 bg-gray-50 text-gray-600 hover:border-purple-300 hover:bg-purple-50',
              ]"
            >
              {{ option.label }}
            </button>
          </div>
        </NFormItem>

        <!-- 描述文本 -->
        <div class="mb-16px rounded-8px bg-blue-50 p-12px">
          <p class="text-14px text-gray-600 leading-20px">
            <SvgIcon icon="mdi:information-outline" class="mr-4px inline text-blue-500" />
            输入想要生成教案的标题，AI 会自动调用学科课标、教学规律，生成完整教案框架
          </p>
        </div>

        <!-- 其他要求 -->
        <NFormItem label="其他要求" path="otherRequirements">
          <template #label>
            <span class="text-left text-14px text-[#464646] font-500">其他要求</span>
          </template>
          <NInput
            v-model:value="formModel.otherRequirements"
            type="textarea"
            placeholder="请输入其他特殊要求（可选）"
            :rows="3"
            clearable
            maxlength="500"
            show-count
          />
        </NFormItem>
      </Nscrollbar>
    </div>

    <!-- 生成按钮区域 -->
    <div class="mt-24px flex-shrink-0 rounded-8px bg-blue-50 p-16px text-center">
      <NButton
        type="primary"
        size="large"
        class="mb-12px from-blue-500 to-purple-500 bg-gradient-to-r px-24px py-12px"
        @click="generateLesson"
      >
        <template #icon>
          <SvgIcon icon="mdi:magic-staff" />
        </template>
        一键生成教案
      </NButton>
      <p class="text-12px text-gray-500">
        内容由AI生成，仅供参考。
      </p>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
